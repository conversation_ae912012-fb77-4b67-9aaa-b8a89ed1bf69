import React, { useState, useEffect } from "react";
import {
  SafeAreaView,
  TouchableOpacity,
  Text,
  TextInput,
  View,
  StyleSheet,
  StatusBar,
  FlatList,
  Alert,
  Platform,
  PermissionsAndroid,
} from "react-native";
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

// Import VideoSDK modules directly
import {
  MeetingProvider,
  useMeeting,
  useParticipant,
  MediaStream,
  RTCView
} from "@videosdk.live/react-native-sdk";

import { createMeeting, token } from "../api";

/**
 * VideoMeeting Component - Cross-Platform Video Calling
 *
 * This component enables real-time video consultation between mobile and web users.
 *
 * How to test cross-platform functionality:
 * 1. Create a meeting on the web application (this will generate a meeting ID like "abcd-efgh-ijkl")
 * 2. On the mobile app, tap "Join Existing Meeting"
 * 3. Enter the meeting ID from step 1
 * 4. The mobile app should automatically join the same meeting
 * 5. Both web and mobile users should see each other in the video call
 *
 * Key features:
 * - Auto-join when valid meeting ID is provided
 * - Real-time participant detection
 * - Cross-platform compatibility with VideoSDK
 * - Proper error handling and user feedback
 */

function JoinScreen(props) {
  const [meetingVal, setMeetingVal] = useState("");
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" />
      
      {/* Header */}
      <LinearGradient
        colors={['#66948a', '#66948a']}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Video Consultation</Text>
        <View style={styles.headerSpacer} />
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Start Your Session</Text>
          <Text style={styles.welcomeSubtitle}>
            Connect with mental health professionals through secure video calls
          </Text>
        </View>

        <TouchableOpacity
          onPress={() => {
            props.getMeetingId();
          }}
          style={styles.createMeetingButton}
        >
          <LinearGradient
            colors={['#4A90E2', '#357ABD']}
            style={styles.buttonGradient}
          >
            <Text style={styles.buttonText}>Create New Meeting</Text>
          </LinearGradient>
        </TouchableOpacity>

        <View style={styles.divider}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>OR</Text>
          <View style={styles.dividerLine} />
        </View>

        <View style={styles.joinSection}>
          <Text style={styles.joinTitle}>Join Existing Meeting</Text>
          <TextInput
            value={meetingVal}
            onChangeText={setMeetingVal}
            placeholder="Enter Meeting ID (XXXX-XXXX-XXXX)"
            placeholderTextColor="#999"
            style={styles.meetingInput}
          />
          <TouchableOpacity
            style={styles.joinMeetingButton}
            onPress={() => {
              console.log("�🚀🚀 JOIN MEETING BUTTON CLICKED 🚀🚀🚀");
              console.log("📝 RAW INPUT FROM TEXT FIELD:", `"${meetingVal}"`);
              console.log("� INPUT TYPE:", typeof meetingVal);
              console.log("� INPUT LENGTH:", meetingVal.length);
              console.log("� INPUT AFTER TRIM:", `"${meetingVal.trim()}"`);
              console.log("📝 TRIMMED LENGTH:", meetingVal.trim().length);
              console.log("� WEB MEETING ID:", "12l7-y2x8-u5nn");
              console.log("📱 MOBILE INPUT ID:", meetingVal.trim());
              console.log("🔍 CHARACTER BY CHARACTER COMPARISON:");
              const webId = "12l7-y2x8-u5nn";
              const mobileId = meetingVal.trim();
              for (let i = 0; i < Math.max(webId.length, mobileId.length); i++) {
                const webChar = webId[i] || 'undefined';
                const mobileChar = mobileId[i] || 'undefined';
                const match = webChar === mobileChar ? "✅" : "❌";
                console.log(`Position ${i}: Web="${webChar}" Mobile="${mobileChar}" ${match}`);
              }
              console.log("🎯 FINAL MATCH RESULT:", mobileId === webId ? "✅ PERFECT MATCH!" : "❌ MISMATCH!");
              console.log("🚀 Calling getMeetingId with:", `"${meetingVal.trim()}"`);
              props.getMeetingId(meetingVal.trim());
            }}
          >
            <Text style={styles.joinButtonText}>Join Meeting</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const Button = ({ onPress, buttonText, backgroundColor }) => {
  const handlePress = () => {
    console.log(`Button "${buttonText}" pressed`);
    if (onPress) {
      onPress();
    } else {
      console.log(`No onPress function provided for "${buttonText}"`);
    }
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      style={[styles.controlButton, { backgroundColor }]}
    >
      <Text style={styles.controlButtonText}>{buttonText}</Text>
    </TouchableOpacity>
  );
};

function ControlsContainer({ join, leave, toggleWebcam, toggleMic, participantsArrId, localParticipant, setPreventAutoLeave, hasJoined, webcamOn, micOn, isJoining, connectionTimeout, setIsJoining, setConnectionTimeout, setIsLeavingIntentionally }) {
  const handleJoin = () => {
    console.log('🔗 Manual join button pressed');
    console.log('🔗 Attempting to join existing meeting...');
    setIsJoining(true);
    setConnectionTimeout(false);
    try {
      join();
      console.log('🔗 Join function called successfully');
    } catch (error) {
      console.log('❌ Error joining meeting:', error);
      setIsJoining(false);
      setConnectionTimeout(true);
    }
  };

  const handleToggleWebcam = () => {
    console.log('🎥 Camera button pressed, current state:', webcamOn);
    console.log('toggleWebcam function type:', typeof toggleWebcam);
    try {
      const result = toggleWebcam();
      console.log('toggleWebcam result:', result);
    } catch (error) {
      console.log('Error toggling webcam:', error);
    }
  };

  const handleToggleMic = () => {
    console.log('🎤 Mic button pressed, current state:', micOn);
    console.log('toggleMic function type:', typeof toggleMic);
    try {
      const result = toggleMic();
      console.log('toggleMic result:', result);
    } catch (error) {
      console.log('Error toggling mic:', error);
    }
  };

  const handleLeave = () => {
    console.log('🚪 Leave button pressed - this is INTENTIONAL');
    console.log('leave function type:', typeof leave);

    // Mark this as an intentional leave BEFORE calling leave()
    console.log('🔴 Setting intentional leave flag BEFORE leaving');
    setIsLeavingIntentionally(true);

    try {
      const result = leave();
      console.log('✅ Leave function called successfully, result:', result);
    } catch (error) {
      console.log('❌ Error leaving meeting:', error);
      setIsLeavingIntentionally(false); // Reset flag on error
    }
  };

  return (
    <View style={styles.controlsContainer}>
      {!hasJoined && !isJoining && (
        <Button
          onPress={handleJoin}
          buttonText="Join Meeting"
          backgroundColor="#28a745"
        />
      )}
      {!hasJoined && isJoining && (
        <Button
          onPress={() => {}}
          buttonText="Joining..."
          backgroundColor="#FFA500"
        />
      )}
      {!hasJoined && connectionTimeout && (
        <Button
          onPress={handleJoin}
          buttonText="Retry Join"
          backgroundColor="#FF6B6B"
        />
      )}
      {hasJoined && (
        <>
          <Button
            onPress={handleToggleWebcam}
            buttonText={webcamOn ? "📹 Camera On" : "📹 Camera Off"}
            backgroundColor={webcamOn ? "#4CAF50" : "#757575"}
          />
          <Button
            onPress={handleToggleMic}
            buttonText={micOn ? "🎤 Mic On" : "🎤 Mic Off"}
            backgroundColor={micOn ? "#4CAF50" : "#757575"}
          />
        </>
      )}
      <Button
        onPress={handleLeave}
        buttonText="Leave"
        backgroundColor="#FF4757"
      />
    </View>
  );
}

function ParticipantView({ participantId }) {
  const { webcamStream, webcamOn } = useParticipant(participantId);

  return webcamOn && webcamStream ? (
    <RTCView
      streamURL={new MediaStream([webcamStream.track]).toURL()}
      objectFit="cover"
      style={styles.participantVideo}
    />
  ) : (
    <View style={styles.noVideoContainer}>
      <Text style={styles.noVideoText}>Camera Off</Text>
    </View>
  );
}

function ParticipantList({ participants, hasJoined, isJoining, connectionTimeout, meetingId }) {
  console.log('ParticipantList participants:', participants, 'length:', participants?.length);

  if (participants && participants.length > 0) {
    return (
      <FlatList
        data={participants}
        renderItem={({ item }) => (
          <ParticipantView participantId={item} />
        )}
        style={styles.participantsList}
      />
    );
  }

  // Show different messages based on connection state
  if (connectionTimeout) {
    return (
      <View style={styles.waitingContainer}>
        <Text style={styles.waitingText}>⚠️ Connection Failed</Text>
        <Text style={styles.waitingSubtext}>Unable to join meeting. Please check the meeting ID and try again.</Text>
        <Text style={styles.meetingIdDisplay}>Meeting ID: {meetingId}</Text>
      </View>
    );
  }

  if (isJoining) {
    return (
      <View style={styles.waitingContainer}>
        <Text style={styles.waitingText}>🔄 Joining Meeting...</Text>
        <Text style={styles.waitingSubtext}>Connecting to video session</Text>
        <Text style={styles.meetingIdDisplay}>Meeting ID: {meetingId}</Text>
      </View>
    );
  }

  if (hasJoined) {
    return (
      <View style={styles.waitingContainer}>
        <Text style={styles.waitingText}>✅ Connected to Meeting</Text>
        <Text style={styles.waitingSubtext}>Waiting for other participants to join</Text>
        <Text style={styles.meetingIdDisplay}>Meeting ID: {meetingId}</Text>
      </View>
    );
  }

  return (
    <View style={styles.waitingContainer}>
      <Text style={styles.waitingText}>Ready to join meeting</Text>
      <Text style={styles.waitingSubtext}>Connecting automatically...</Text>
      <Text style={styles.meetingIdDisplay}>Meeting ID: {meetingId}</Text>
    </View>
  );
}

function MeetingView() {
  const [hasJoined, setHasJoined] = useState(false);
  const [preventAutoLeave, setPreventAutoLeave] = useState(false);
  const [connectionTimeout, setConnectionTimeout] = useState(false);
  const [isJoining, setIsJoining] = useState(false);
  const [isLeavingIntentionally, setIsLeavingIntentionally] = useState(false);
  const [connectionStable, setConnectionStable] = useState(false);

  const { join, leave, toggleWebcam, toggleMic, participants, localParticipant, meetingId, localWebcamOn, localMicOn } = useMeeting({
    onError: (error) => {
      console.log('❌ Meeting error:', error);
      console.log('❌ Error details:', JSON.stringify(error, null, 2));
      console.log('❌ Error code:', error.code);
      console.log('❌ Error message:', error.message);
      console.log('❌ Meeting ID that failed:', meetingId);

      // Check if it's a meeting not found error
      if (error.code === '4001' || error.code === '4001' || error.message?.includes('not found') || error.message?.includes('invalid')) {
        console.log('🚫 Meeting not found or invalid - this meeting may not exist');
      }

      setIsJoining(false);
      setConnectionTimeout(true);
    },
    onMeetingJoined: () => {
      console.log('✅ Meeting joined successfully!');
      console.log('🎉 Meeting ID we joined:', meetingId);
      console.log('🎉 Local participant ID:', localParticipant?.id);
      console.log('🎉 Current participants count:', participants?.size || 0);
      console.log('🎉 Join timestamp:', new Date().toISOString());

      // Reset all flags for successful join
      setHasJoined(true);
      setIsJoining(false);
      setConnectionTimeout(false);

      // CRITICAL: Prevent auto-leave immediately after joining
      console.log('🔒 Setting preventAutoLeave to prevent immediate disconnection');
      setPreventAutoLeave(false); // Keep this false to allow normal operation

      // Log the meeting state after joining and check for other participants
      setTimeout(() => {
        console.log('📊 Meeting state 1 second after join:', {
          hasJoined: true,
          participantCount: participants?.size || 0,
          localParticipantId: localParticipant?.id,
          meetingId,
          participantIds: [...participants.keys()],
          stillConnected: hasJoined // This should still be true
        });

        // Check if we're the only participant
        if (participants?.size <= 1) {
          console.log('⚠️ Only 1 participant in meeting - might be connection issue or web user not connected');
          console.log('💡 Web user should appear as second participant if connection is working');
        } else {
          console.log('✅ Multiple participants detected - cross-platform connection working!');
        }
      }, 1000);

      // Check again after 3 seconds to see if more participants join
      setTimeout(() => {
        console.log('📊 Meeting state 3 seconds after join:', {
          participantCount: participants?.size || 0,
          participantIds: [...participants.keys()],
          stillConnected: hasJoined,
          meetingId
        });

        if (!hasJoined) {
          console.log('🚨 CONNECTION LOST! Mobile app disconnected within 3 seconds');
        }
      }, 3000);
    },
    onParticipantJoined: (participant) => {
      console.log('👥 NEW PARTICIPANT JOINED:', participant.displayName || participant.id);
      console.log('👥 Participant details:', JSON.stringify(participant, null, 2));
    },
    onParticipantLeft: (participant) => {
      console.log('👋 PARTICIPANT LEFT:', participant.displayName || participant.id);
    },
    onMeetingLeft: () => {
      console.log('⚠️ MEETING LEFT EVENT TRIGGERED!');
      console.log('🕐 Time of leave:', new Date().toISOString());
      console.log('🔍 Was this intentional?', isLeavingIntentionally);
      console.log('🔍 Meeting state when left:', {
        hasJoined,
        isJoining,
        preventAutoLeave,
        connectionTimeout,
        participantCount: participants?.size || 0
      });

      // Always set hasJoined to false when meeting is left
      setHasJoined(false);
      setIsJoining(false);

      // Only show warning if this was NOT intentional
      if (isLeavingIntentionally) {
        console.log('🔴 Intentional leave - user clicked leave button');
        setPreventAutoLeave(true);
      } else {
        console.log('🚨 UNEXPECTED LEAVE - This is the problem we need to fix!');
        console.log('🚨 Possible causes:');
        console.log('  - Network connection issue');
        console.log('  - Meeting ID mismatch');
        console.log('  - Token authentication problem');
        console.log('  - VideoSDK configuration issue');

        // Don't prevent rejoining - let user try again
        // But add a small delay to prevent rapid reconnection loops
        setTimeout(() => {
          console.log('🔄 Ready for reconnection attempt');
        }, 2000);
      }

      // Reset the intentional leave flag
      setIsLeavingIntentionally(false);
    },
  });
  const participantsArrId = [...participants.keys()];

  // Add connection timeout
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!hasJoined && isJoining) {
        console.log('⏰ Connection timeout - meeting taking too long to join');
        setConnectionTimeout(true);
        setIsJoining(false);
      }
    }, 15000); // 15 second timeout

    return () => clearTimeout(timer);
  }, [hasJoined, isJoining]);

  // AUTO-JOIN functionality - automatically join when meeting is ready
  useEffect(() => {
    console.log('🚀 MeetingView mounted - checking auto-join conditions...');
    console.log('🔍 Auto-join conditions:', {
      hasJoin: !!join,
      hasJoined,
      preventAutoLeave,
      isJoining,
      connectionTimeout,
      meetingId
    });

    // Delay to ensure MeetingProvider is fully ready
    const autoJoinTimer = setTimeout(() => {
      if (join && !hasJoined && !preventAutoLeave && !isJoining && !connectionTimeout) {
        console.log('🔗 Auto-joining meeting with ID:', meetingId);
        console.log('🔗 Meeting format validation:', {
          meetingId,
          hasHyphens: meetingId?.includes('-'),
          length: meetingId?.length,
          isValidFormat: /^[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}$/.test(meetingId || '')
        });

        setIsJoining(true);
        try {
          join();
          console.log('🔗 Join function called successfully');
        } catch (error) {
          console.log('❌ Auto-join error:', error);
          setIsJoining(false);
          setConnectionTimeout(true);
        }
      } else {
        console.log('🚫 Auto-join skipped:', {
          hasJoin: !!join,
          hasJoined,
          preventAutoLeave,
          isJoining,
          connectionTimeout,
          meetingId
        });
      }
    }, 1500); // 1.5 second delay

    return () => clearTimeout(autoJoinTimer);
  }, [join, meetingId]); // Depend on both join function and meetingId

  // Debug the meeting functions and state
  console.log('Meeting functions available:', {
    join: typeof join,
    leave: typeof leave,
    toggleWebcam: typeof toggleWebcam,
    toggleMic: typeof toggleMic,
    participantsCount: participantsArrId.length,
    localParticipant: localParticipant ? localParticipant.id : 'none',
    webcamOn: localWebcamOn,
    micOn: localMicOn,
    hasJoined
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" />
      
      {/* Header */}
      <LinearGradient
        colors={['#66948a', '#66948a']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitleCentered}>Video Session</Text>
          {meetingId && (
            <Text style={styles.headerMeetingId}>Meeting ID: {meetingId}</Text>
          )}
        </View>
      </LinearGradient>

      <View style={styles.meetingContent}>
        <ParticipantList
          participants={participantsArrId}
          hasJoined={hasJoined}
          isJoining={isJoining}
          connectionTimeout={connectionTimeout}
          meetingId={meetingId}
        />
        <ControlsContainer
          join={join}
          leave={leave}
          toggleWebcam={toggleWebcam}
          toggleMic={toggleMic}
          participantsArrId={participantsArrId}
          localParticipant={localParticipant}
          setPreventAutoLeave={setPreventAutoLeave}
          hasJoined={hasJoined}
          webcamOn={localWebcamOn}
          micOn={localMicOn}
          isJoining={isJoining}
          connectionTimeout={connectionTimeout}
          setIsJoining={setIsJoining}
          setConnectionTimeout={setConnectionTimeout}
          setIsLeavingIntentionally={setIsLeavingIntentionally}
        />
      </View>
    </SafeAreaView>
  );
}

export default function VideoMeeting() {
  const [meetingId, setMeetingId] = useState(null);
  const [isVideoSDKAvailable, setIsVideoSDKAvailable] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if VideoSDK is available
    setIsVideoSDKAvailable(!!MeetingProvider && !!createMeeting && !!token);
    console.log("VideoSDK availability check:", {
      MeetingProvider: !!MeetingProvider,
      createMeeting: !!createMeeting,
      token: !!token,
      tokenValue: token ? `${token.substring(0, 50)}...` : 'NO_TOKEN'
    });

    // Validate token format
    if (token) {
      console.log("🔑 Token validation:", {
        isJWT: token.includes('.'),
        parts: token.split('.').length,
        startsWithEyJ: token.startsWith('eyJ'),
        length: token.length
      });
    }

    // Request permissions when component mounts
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    try {
      // Request camera and microphone permissions explicitly
      console.log('🔐 Requesting camera and microphone permissions...');

      // For React Native, we need to request permissions through the platform
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        console.log('📱 Android permissions granted:', granted);

        if (granted['android.permission.CAMERA'] === PermissionsAndroid.RESULTS.GRANTED &&
            granted['android.permission.RECORD_AUDIO'] === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('✅ All permissions granted');
        } else {
          console.log('❌ Some permissions denied');
        }
      } else {
        console.log('📱 iOS permissions will be requested by VideoSDK when needed');
      }
    } catch (error) {
      console.log('❌ Permission request error:', error);
    }
  };

  const getMeetingId = async (id: string | null = null) => {
    console.log("🚀 getMeetingId called with:", {
      id,
      idType: typeof id,
      idLength: id?.length,
      createMeeting: !!createMeeting,
      token: !!token
    });

    console.log("🎯 CRITICAL DEBUG - Meeting ID Processing:");
    console.log("🎯 Raw input:", id);
    console.log("🎯 Expected web meeting ID: 12l7-y2x8-u5nn");
    console.log("🎯 Input matches expected:", id === "12l7-y2x8-u5nn" ? "✅ YES" : "❌ NO");

    if (!createMeeting || !token) {
      console.log("VideoSDK not available - showing alert");
      Alert.alert(
        "VideoSDK Not Available",
        "Video calling requires a development build. Please build the app with native modules enabled.",
        [
          { text: "Go Back", onPress: () => router.back() },
          { text: "Learn More", onPress: () => showVideoSDKInfo() }
        ]
      );
      return;
    }

    try {
      if (id == null || id.trim() === "") {
        console.log("🔧 Creating new meeting...");
        const meetingId = await createMeeting({ token });
        console.log("✅ Meeting created successfully:", meetingId);
        setMeetingId(meetingId);
      } else {
        const trimmedId = id.trim();
        console.log("🔧 JOINING EXISTING meeting with ID:", trimmedId);
        console.log("🎯 WEB MEETING ID: 12l7-y2x8-u5nn");
        console.log("🎯 MOBILE INPUT ID:", trimmedId);
        console.log("🎯 IDs MATCH:", trimmedId === "12l7-y2x8-u5nn" ? "✅ PERFECT!" : "❌ MISMATCH!");

        console.log("🔍 Meeting ID format check:", {
          id: trimmedId,
          length: trimmedId.length,
          format: /^[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}$/.test(trimmedId)
        });

        // Validate meeting ID format - VideoSDK accepts various formats
        if (!isValidMeetingId(trimmedId)) {
          throw new Error("Invalid meeting ID format. Expected format: xxxx-xxxx-xxxx");
        }

        console.log("✅ Valid meeting ID - setting up to join existing meeting");
        console.log("🎯 IMPORTANT: This should join the EXISTING meeting, not create a new one");
        console.log("🎯 Expected behavior: Mobile should connect to web user's meeting");
        console.log("🎯 Final meeting ID being set:", trimmedId);

        // Verify the meeting exists by making a test API call
        await validateMeetingExists(trimmedId);
        setMeetingId(trimmedId);
      }
    } catch (error) {
      console.log("❌ Meeting creation/join error:", error);
      console.log("❌ Error details:", JSON.stringify(error, null, 2));
      Alert.alert("Error", "Failed to create/join meeting: " + error.message);
    }
  };

  // Helper function to validate meeting ID format
  const isValidMeetingId = (meetingId: string) => {
    console.log('🔍 Validating meeting ID format:', {
      meetingId,
      length: meetingId.length,
      hasHyphens: meetingId.includes('-'),
      hyphenCount: (meetingId.match(/-/g) || []).length
    });

    // VideoSDK meeting IDs can be in various formats:
    // - xxxx-xxxx-xxxx (4-4-4 format like your web meeting: 12f7-y2e8-u6nn)
    // - longer formats with more segments
    const patterns = [
      /^[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}$/, // lowercase 4-4-4 (like 12f7-y2e8-u6nn)
      /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/, // uppercase 4-4-4
      /^[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}$/, // mixed case 4-4-4
      /^[a-zA-Z0-9]+-[a-zA-Z0-9]+-[a-zA-Z0-9]+$/, // flexible length with hyphens
      /^[a-zA-Z0-9]{8,}$/ // no hyphens, just alphanumeric
    ];

    const isValid = patterns.some(pattern => pattern.test(meetingId));
    console.log('🔍 Meeting ID validation result:', {
      meetingId,
      isValid,
      matchedPattern: patterns.findIndex(pattern => pattern.test(meetingId))
    });

    return isValid;
  };

  // Helper function to validate if meeting exists
  const validateMeetingExists = async (meetingId: string) => {
    try {
      console.log("🔍 Validating meeting exists:", meetingId);
      // VideoSDK doesn't have a direct "check meeting exists" endpoint,
      // but we can validate the format and let the join process handle invalid IDs
      // The meeting validation will happen when we actually try to join
      return true;
    } catch (error) {
      console.log("❌ Meeting validation error:", error);
      throw new Error("Unable to validate meeting ID. Please check the ID and try again.");
    }
  };

  const showVideoSDKInfo = () => {
    Alert.alert(
      "Development Build Required",
      "To use video calling features:\n\n1. Install Expo Dev Client\n2. Build a development build\n3. Install the custom build on your device\n\nThis enables native WebRTC modules required for video calls.",
      [{ text: "OK" }]
    );
  };

  if (!isVideoSDKAvailable) {
    return <VideoSDKNotAvailable />;
  }

  console.log('🔍 VideoMeeting render state:', {
    meetingId,
    hasToken: !!token,
    tokenPrefix: token ? token.substring(0, 20) + '...' : 'NO_TOKEN',
    isVideoSDKAvailable,
    timestamp: new Date().toISOString()
  });

  // Log meeting creation vs joining
  if (meetingId) {
    console.log('📋 Meeting Info:', {
      meetingId,
      isJoiningExisting: meetingId.includes('-'),
      action: meetingId.includes('-') ? 'JOINING_EXISTING' : 'CREATING_NEW',
      meetingIdLength: meetingId.length
    });
  }

  return meetingId ? (
    <>
      {console.log('🚀 Creating MeetingProvider with:', {
        meetingId,
        token: !!token,
        action: meetingId.includes('-') ? 'JOINING_EXISTING' : 'CREATING_NEW'
      })}
      <MeetingProvider
        config={{
          meetingId,
          micEnabled: false,  // Start with mic disabled to prevent issues
          webcamEnabled: false, // Start with camera disabled to prevent issues
          name: "MentalEase Mobile User",
          // Add debug metadata to track this connection
          metaData: {
            source: "mobile_app",
            platform: "react_native",
            timestamp: new Date().toISOString(),
            version: "1.0.0"
          }
        }}
        token={token}
      >
        <MeetingView />
      </MeetingProvider>
    </>
  ) : (
    <JoinScreen getMeetingId={getMeetingId} />
  );
}

// Component to show when VideoSDK is not available
function VideoSDKNotAvailable() {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#66948a" barStyle="light-content" />

      {/* Header */}
      <LinearGradient
        colors={['#66948a', '#66948a']}
        style={styles.header}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Video Consultation</Text>
        <View style={styles.headerSpacer} />
      </LinearGradient>

      <View style={styles.content}>
        <View style={styles.notAvailableContainer}>
          <Text style={styles.notAvailableTitle}>🚧 Development Build Required</Text>
          <Text style={styles.notAvailableText}>
            Video calling features require native WebRTC modules that are not available in Expo Go.
          </Text>
          <Text style={styles.notAvailableSubtext}>
            To enable video calls:
            {'\n'}• Build a development build with EAS
            {'\n'}• Install the custom build on your device
            {'\n'}• This enables native video calling capabilities
          </Text>

          <TouchableOpacity
            style={styles.backToDashboardButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backToDashboardButtonText}>Back to Dashboard</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: 45,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerTitleCentered: {
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    width: '100%',
  },
  headerContent: {
    alignItems: 'center',
    width: '100%',
  },
  headerMeetingId: {
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    marginTop: 4,
    opacity: 0.9,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  welcomeSection: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 20,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 10,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 24,
  },
  createMeetingButton: {
    borderRadius: 25,
    marginBottom: 30,
    shadowColor: '#4A90E2',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  buttonGradient: {
    paddingVertical: 16,
    borderRadius: 25,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 30,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E0E0E0',
  },
  dividerText: {
    marginHorizontal: 20,
    color: '#999',
    fontSize: 14,
    fontWeight: '500',
  },
  joinSection: {
    alignItems: 'center',
  },
  joinTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 20,
  },
  meetingInput: {
    width: '100%',
    padding: 16,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderRadius: 15,
    fontSize: 16,
    color: '#2C3E50',
    backgroundColor: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
  },
  joinMeetingButton: {
    backgroundColor: '#66948a',
    paddingVertical: 14,
    paddingHorizontal: 40,
    borderRadius: 25,
  },
  joinButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  meetingContent: {
    flex: 1,
  },
  participantsList: {
    flex: 1,
  },
  participantVideo: {
    height: 300,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 15,
  },
  noVideoContainer: {
    backgroundColor: '#E8E8E8',
    height: 300,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noVideoText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  waitingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  waitingText: {
    fontSize: 18,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 26,
  },
  waitingSubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 10,
  },
  meetingIdDisplay: {
    fontSize: 12,
    color: '#66948a',
    textAlign: 'center',
    marginTop: 15,
    fontWeight: '600',
    backgroundColor: '#F0F8F0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    alignSelf: 'center',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  controlButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 20,
    minWidth: 70,
    alignItems: 'center',
  },
  controlButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  notAvailableContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  notAvailableTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 20,
  },
  notAvailableText: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  notAvailableSubtext: {
    fontSize: 14,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 40,
  },
  backToDashboardButton: {
    backgroundColor: '#66948a',
    paddingVertical: 14,
    paddingHorizontal: 30,
    borderRadius: 25,
  },
  backToDashboardButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
