// Get VideoSDK token from environment variables
export const token = process.env.EXPO_PUBLIC_VIDEOSDK_TOKEN;

// API call to create meeting
export const createMeeting = async ({ token }) => {
  console.log("🔧 Creating meeting with token:", token ? `${token.substring(0, 20)}...` : 'NO_TOKEN');

  const res = await fetch(`https://api.videosdk.live/v2/rooms`, {
    method: "POST",
    headers: {
      authorization: `${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({}),
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.log("❌ Meeting creation failed:", {
      status: res.status,
      statusText: res.statusText,
      error: errorText
    });
    throw new Error(`Failed to create meeting: ${res.status} ${res.statusText}`);
  }

  const data = await res.json();
  console.log("✅ Meeting creation response:", data);

  const { roomId } = data;
  return roomId;
};

// API call to validate if a meeting exists
export const validateMeeting = async ({ meetingId, token }) => {
  console.log("🔍 Validating meeting:", meetingId);

  try {
    const res = await fetch(`https://api.videosdk.live/v2/rooms/validate/${meetingId}`, {
      method: "GET",
      headers: {
        authorization: `${token}`,
        "Content-Type": "application/json",
      },
    });

    const data = await res.json();
    console.log("📋 Meeting validation response:", data);
    return data;
  } catch (error) {
    console.log("❌ Meeting validation error:", error);
    return { valid: false, error: error.message };
  }
};