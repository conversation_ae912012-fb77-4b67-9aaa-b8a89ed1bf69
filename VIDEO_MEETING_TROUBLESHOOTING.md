# Video Meeting Cross-Platform Troubleshooting Guide

## Current Issue Analysis

Based on your logs, the mobile app is successfully joining the meeting but immediately leaving due to an "unexpected leave" detection. This prevents cross-platform connectivity.

### Log Analysis:
```
✅ Meeting joined successfully
❌ Meeting left successfully (unexpected)
🔴 Unexpected leave detected - preventing auto-rejoin
```

## Potential Causes & Solutions

### 1. **Token Mismatch (Most Likely)**
**Problem**: Web and mobile apps using different VideoSDK tokens
**Solution**: Ensure both platforms use the same token

**Check your web application's VideoSDK token:**
- Compare with mobile app token in `.env` file
- Both should be identical: `EXPO_PUBLIC_VIDEOSDK_TOKEN`

### 2. **Meeting State Issues**
**Problem**: Meeting created on web might not be in proper state
**Solution**: 
- Ensure web meeting is fully initialized before mobile joins
- Check if web user has properly joined the meeting

### 3. **VideoSDK Version Compatibility**
**Problem**: Different VideoSDK versions between platforms
**Current mobile version**: `@videosdk.live/react-native-sdk": "^0.3.6"`
**Solution**: Verify web app uses compatible VideoSDK version

### 4. **Permission Issues**
**Problem**: Mobile app lacks proper permissions
**Solution**: Ensure camera/microphone permissions are granted

## Debugging Steps

### Step 1: Verify Token Configuration
1. Check `.env` file: `EXPO_PUBLIC_VIDEOSDK_TOKEN`
2. Verify web app uses same token
3. Ensure token has proper permissions: `["allow_join"]`

### Step 2: Manual Join Testing
1. Disable auto-join (currently disabled for debugging)
2. Create meeting on web
3. Enter meeting ID on mobile
4. Manually press "Join Meeting" button
5. Monitor logs for immediate leave

### Step 3: Cross-Platform Token Test
```javascript
// Add this to your web application console
console.log('Web VideoSDK Token:', YOUR_VIDEOSDK_TOKEN);

// Compare with mobile app logs:
// "VideoSDK availability check: { token: true, tokenValue: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }"
```

### Step 4: Meeting Validation
1. Create meeting on web
2. Note the exact meeting ID format
3. Verify mobile app accepts the format
4. Check if meeting exists before joining

## Quick Fixes Applied

### 1. **Disabled Auto-Join**
- Temporarily disabled to allow manual control
- Prevents immediate join/leave cycle

### 2. **Improved Leave Detection**
- Fixed logic to not prevent rejoining on unexpected leaves
- Better handling of intentional vs unintentional leaves

### 3. **Enhanced Logging**
- Added detailed token validation
- Better meeting state tracking
- Improved error reporting

## Testing Instructions

### Test 1: Basic Connection
1. Create meeting on web application
2. Copy the meeting ID (format: `xxxx-xxxx-xxxx`)
3. Open mobile app → Video Meeting → Join Existing Meeting
4. Enter meeting ID and press "Join Meeting"
5. Check logs for join/leave cycle

### Test 2: Token Verification
1. Compare tokens between platforms
2. Ensure both use same VideoSDK API key
3. Verify token permissions include meeting join

### Test 3: Meeting State
1. Ensure web user is actively in meeting
2. Try joining from mobile while web user is connected
3. Check if both users appear in participant list

## Expected Behavior
1. Mobile enters meeting ID
2. App shows "Joining..." status
3. Successfully joins meeting
4. Shows "Connected to Meeting"
5. Both web and mobile users see each other
6. Video/audio controls work properly

## Next Steps
1. **Enable auto-join** after fixing immediate leave issue
2. **Add meeting validation** before joining
3. **Implement retry logic** for failed connections
4. **Add participant count display** for better feedback

## Contact Points
- Check console logs on both platforms
- Verify network connectivity
- Ensure VideoSDK service is operational
- Test with different meeting IDs
